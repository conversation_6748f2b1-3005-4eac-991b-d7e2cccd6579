import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Mail, RefreshCw } from "lucide-react";
import { toast } from "sonner";

export default function EmailVerificationSuccess() {
  const navigate = useNavigate();
  const location = useLocation();

  // Get email from navigation state
  const email = location.state?.email;

  // State for resend button
  const [showResendButton, setShowResendButton] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);

  // Show resend button after 30 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowResendButton(true);
    }, 30000); // 30 seconds

    return () => clearTimeout(timer);
  }, []);

  const handleGoToLogin = () => {
    navigate("/login");
  };

  const handleResendEmail = async () => {
    if (!email) {
      toast.error("Email address not found. Please try registering again.");
      return;
    }

    setIsResending(true);
    setResendDisabled(true);

    try {
      // Simulate API call - replace with actual API endpoint
      // await axios.post("http://localhost:3000/api/auth/resend-verification", { email });

      // For now, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success("Verification email sent again!");

      // Re-enable button after 60 seconds
      setTimeout(() => {
        setResendDisabled(false);
      }, 60000);

    } catch (error) {
      toast.error("Failed to resend verification email. Please try again.");
      setResendDisabled(false);
    } finally {
      setIsResending(false);
    }
  };

  return (
   <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardContent className="pt-8 pb-8">
          <div className="text-center space-y-8">
            {/* Success Icon */}
            <div className="flex justify-center">
              <div className="h-16 w-16 bg-green-50 dark:bg-green-950 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            </div>
            
            {/* Main Content */}
            <div className="space-y-4">
              <h1 className="text-2xl font-medium text-foreground">
                Check your email
              </h1>
              
              <div className="space-y-3">
                <p className="text-muted-foreground text-sm leading-relaxed">
                  We've sent a verification link to
                </p>
                
                {email ? (
                  <div className="bg-muted/50 rounded-lg px-4 py-3 border">
                    <p className="text-sm font-medium text-foreground">
                      {email}
                    </p>
                  </div>
                ) : (
                  <div className="bg-muted/50 rounded-lg px-4 py-3 border">
                    <p className="text-sm text-muted-foreground">
                      your registered email address
                    </p>
                  </div>
                )}
                
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Click the link in the email to activate your account.
                </p>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={handleGoToLogin}
                className="w-full"
                size="lg"
              >
                Continue to Login
              </Button>

              {/* Resend Button - shown after delay */}
              {showResendButton && (
                <Button
                  onClick={handleResendEmail}
                  variant="outline"
                  className="w-full"
                  size="lg"
                  disabled={resendDisabled || isResending}
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4" />
                      Resend verification email
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* Help Text */}
            <div className="space-y-2">
              <p className="text-xs text-muted-foreground">
                Didn't receive the email? Check your spam folder
              </p>
              {!showResendButton && (
                <p className="text-xs text-muted-foreground">
                  Resend option will appear in a moment
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
